export const CATEGORY_CONTENT = {
  ai: {
    title: "Artificial Intelligence",
    description: "Explore the cutting-edge world of AI, machine learning, and intelligent systems that are reshaping our future.",
    heroImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1200&h=600&fit=crop",
    color: "from-blue-500 to-purple-600",
    icon: "🤖",
    stats: {
      articles: 45,
      readers: "12K+",
      experts: 8
    },
    topics: [
      "Machine Learning",
      "Deep Learning", 
      "Natural Language Processing",
      "Computer Vision",
      "AI Ethics",
      "Robotics",
      "Neural Networks",
      "AI in Business"
    ],
    featuredExperts: [
      {
        name: "Dr. <PERSON>",
        role: "AI Research Scientist",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
        expertise: "Deep Learning, Computer Vision"
      },
      {
        name: "<PERSON>",
        role: "ML Engineer",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
        expertise: "NLP, AI Ethics"
      }
    ]
  },
  
  technology: {
    title: "Technology",
    description: "Stay updated with the latest tech trends, innovations, and breakthroughs that are transforming industries.",
    heroImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=600&fit=crop",
    color: "from-green-500 to-blue-500",
    icon: "💻",
    stats: {
      articles: 78,
      readers: "25K+",
      experts: 12
    },
    topics: [
      "Web Development",
      "Mobile Apps",
      "Cloud Computing",
      "Cybersecurity",
      "DevOps",
      "Blockchain",
      "IoT",
      "5G Technology"
    ],
    featuredExperts: [
      {
        name: "Alex Rodriguez",
        role: "Full Stack Developer",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
        expertise: "React, Node.js, Cloud Architecture"
      },
      {
        name: "Emily Zhang",
        role: "DevOps Engineer",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
        expertise: "Kubernetes, CI/CD, Infrastructure"
      }
    ]
  },

  ecommerce: {
    title: "E-Commerce",
    description: "Discover strategies, tools, and insights to build and scale successful online businesses in the digital marketplace.",
    heroImage: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=600&fit=crop",
    color: "from-orange-500 to-red-500",
    icon: "🛒",
    stats: {
      articles: 32,
      readers: "8K+",
      experts: 6
    },
    topics: [
      "Online Marketing",
      "Conversion Optimization",
      "Payment Systems",
      "Customer Experience",
      "Inventory Management",
      "Digital Analytics",
      "Social Commerce",
      "Mobile Commerce"
    ],
    featuredExperts: [
      {
        name: "David Kim",
        role: "E-commerce Strategist",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face",
        expertise: "Growth Hacking, Conversion Optimization"
      },
      {
        name: "Lisa Thompson",
        role: "Digital Marketing Expert",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
        expertise: "SEO, Social Media Marketing"
      }
    ]
  },

  trading: {
    title: "Trading & Finance",
    description: "Master the art of trading with expert insights on markets, strategies, and financial technologies.",
    heroImage: "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=1200&h=600&fit=crop",
    color: "from-emerald-500 to-teal-600",
    icon: "📈",
    stats: {
      articles: 28,
      readers: "15K+",
      experts: 5
    },
    topics: [
      "Stock Trading",
      "Cryptocurrency",
      "Technical Analysis",
      "Risk Management",
      "Portfolio Management",
      "Market Psychology",
      "Algorithmic Trading",
      "DeFi"
    ],
    featuredExperts: [
      {
        name: "Michael Foster",
        role: "Quantitative Analyst",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
        expertise: "Algorithmic Trading, Risk Management"
      },
      {
        name: "Rachel Green",
        role: "Crypto Analyst",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
        expertise: "Blockchain, DeFi, Market Analysis"
      }
    ]
  },

  productivity: {
    title: "Productivity",
    description: "Unlock your potential with proven strategies, tools, and techniques to maximize efficiency and achieve your goals.",
    heroImage: "https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=1200&h=600&fit=crop",
    color: "from-purple-500 to-pink-500",
    icon: "⚡",
    stats: {
      articles: 56,
      readers: "18K+",
      experts: 9
    },
    topics: [
      "Time Management",
      "Goal Setting",
      "Workflow Optimization",
      "Digital Tools",
      "Habit Formation",
      "Focus Techniques",
      "Team Collaboration",
      "Work-Life Balance"
    ],
    featuredExperts: [
      {
        name: "James Wilson",
        role: "Productivity Coach",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
        expertise: "Time Management, Goal Achievement"
      },
      {
        name: "Anna Martinez",
        role: "Workflow Specialist",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
        expertise: "Process Optimization, Team Efficiency"
      }
    ]
  },

  lifestyle: {
    title: "Lifestyle",
    description: "Enhance your daily life with insights on wellness, personal development, and modern living in the digital age.",
    heroImage: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=600&fit=crop",
    color: "from-pink-500 to-rose-500",
    icon: "🌟",
    stats: {
      articles: 41,
      readers: "22K+",
      experts: 7
    },
    topics: [
      "Digital Wellness",
      "Personal Development",
      "Health & Fitness",
      "Mindfulness",
      "Travel Tech",
      "Home Automation",
      "Sustainable Living",
      "Creative Pursuits"
    ],
    featuredExperts: [
      {
        name: "Sophie Chen",
        role: "Wellness Expert",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
        expertise: "Digital Wellness, Mindfulness"
      },
      {
        name: "Tom Anderson",
        role: "Lifestyle Blogger",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
        expertise: "Sustainable Living, Tech Integration"
      }
    ]
  }
};

// Helper function to get category data
export const getCategoryData = (slug) => {
  return CATEGORY_CONTENT[slug.toLowerCase()] || null;
};

// Get all category slugs
export const getAllCategorySlugs = () => {
  return Object.keys(CATEGORY_CONTENT);
};
