'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Mail,
  Send,
  Check,
  Star,
  Users,
  TrendingUp,
  Gift,
  Sparkles,
  ArrowRight
} from 'lucide-react';
import { toast } from 'sonner';

export function Newsletter({ variant = "default", className = "" }) {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    setIsSubscribed(true);
    setIsLoading(false);
    toast.success('Welcome to our newsletter! 🎉');

    // Reset after 3 seconds for demo
    setTimeout(() => {
      setIsSubscribed(false);
      setEmail('');
    }, 3000);
  };

  const benefits = [
    { icon: TrendingUp, text: "Latest tech insights & trends" },
    { icon: Star, text: "Exclusive content & early access" },
    { icon: Gift, text: "Free resources & guides" },
    { icon: Users, text: "Join 50K+ tech enthusiasts" }
  ];

  if (variant === "minimal") {
    return (
      <div className={`flex flex-col sm:flex-row gap-3 max-w-md ${className}`}>
        <Input
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="flex-1"
          disabled={isLoading || isSubscribed}
        />
        <Button
          onClick={handleSubmit}
          disabled={!email || isLoading || isSubscribed}
          className="btn-modern"
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Mail className="w-4 h-4" />
            </motion.div>
          ) : isSubscribed ? (
            <Check className="w-4 h-4" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>
    );
  }

  if (variant === "card") {
    return (
      <Card className={`overflow-hidden border-0 shadow-xl bg-gradient-to-br from-primary/5 to-primary/10 ${className}`}>
        <CardContent className="p-8 w-full">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-primary/70 rounded-2xl mb-4">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Stay in the Loop</h3>
            <p className="text-muted-foreground">
              Get the latest articles delivered straight to your inbox
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4 min-w-[500px] w-full">
            <div className="relative min-w-[300px] w-full">
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className=" min-w-[300px] pl-12 pr-4 py-6 rounded-2xl border-2"
                disabled={isLoading || isSubscribed}
              />
              <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            </div>

            <Button
              type="submit"
              disabled={!email || isLoading || isSubscribed}
              className="w-full py-6 text-lg rounded-2xl btn-modern"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Mail className="w-5 h-5" />
                  </motion.div>
                  Subscribing...
                </div>
              ) : isSubscribed ? (
                <div className="flex items-center gap-2">
                  <Check className="w-5 h-5" />
                  Subscribed!
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Send className="w-5 h-5" />
                  Subscribe Now
                </div>
              )}
            </Button>
          </form>

          <div className="grid grid-cols-2 gap-3 mt-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                <benefit.icon className="w-4 h-4 text-primary" />
                <span>{benefit.text}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant - full section
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className={`section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10 ${className}`}
    >
      <div className="container-modern">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="mb-8"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <Sparkles className="w-6 h-6 text-primary" />
              <Badge variant="outline" className="px-4 py-2 text-sm">
                Join Our Community
              </Badge>
              <Sparkles className="w-6 h-6 text-primary" />
            </div>

            <h2 className="text-display mb-6 text-shimmer">
              Never Miss a Beat
            </h2>

            <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
              Subscribe to our newsletter and get the latest insights, trends, and exclusive content
              delivered directly to your inbox every week.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="max-w-xl mx-auto mb-12"
          >
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 w-full">
              <div className="relative flex-1 w-full">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-12 pr-4 py-6 text-lg rounded-2xl border-2 bg-background/50 backdrop-blur-sm"
                  disabled={isLoading || isSubscribed}
                />
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              </div>

              <Button
                type="submit"
                disabled={!email || isLoading || isSubscribed}
                className="py-6 px-8 text-lg rounded-2xl btn-modern whitespace-nowrap"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Mail className="w-5 h-5" />
                    </motion.div>
                    Subscribing...
                  </div>
                ) : isSubscribed ? (
                  <div className="flex items-center gap-2">
                    <Check className="w-5 h-5" />
                    Subscribed!
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    Subscribe Free
                    <ArrowRight className="w-5 h-5" />
                  </div>
                )}
              </Button>
            </form>
          </motion.div>

          {/* Benefits grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                className="flex flex-col items-center text-center p-6 rounded-2xl bg-background/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center mb-4">
                  <benefit.icon className="w-6 h-6 text-primary" />
                </div>
                <p className="text-sm font-medium">{benefit.text}</p>
              </motion.div>
            ))}
          </motion.div>

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-12 text-center"
          >
            <div className="flex items-center justify-center gap-8 text-xs text-muted-foreground">
              <span>✓ No spam, ever</span>
              <span>✓ Unsubscribe anytime</span>
              <span>✓ Weekly digest</span>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
