'use client';

import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Users, BookOpen, Star, Award, ArrowUpRight, Quote } from 'lucide-react';
import Link from 'next/link';

export function AuthorSpotlight({ authors, featuredAuthor }) {
  if (!authors || authors.length === 0) return null;

  // Use the first author as featured if not specified
  const featured = featuredAuthor || authors[0];
  const otherAuthors = authors.filter(author => author.id !== featured.id).slice(0, 3);

  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern bg-gradient-to-br from-purple-50/50 via-background to-blue-50/50 dark:from-purple-950/20 dark:via-background dark:to-blue-950/20"
    >
      <div className="container-modern">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center justify-center gap-2 mb-4"
          >
            <Award className="w-6 h-6 text-purple-500" />
            <h2 className="text-headline text-shimmer">Author Spotlight</h2>
            <Award className="w-6 h-6 text-purple-500" />
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-muted-foreground text-lg max-w-2xl mx-auto"
          >
            Meet the brilliant minds behind our most engaging content
          </motion.p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Featured Author */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <Card className="h-full overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
              <div className="relative p-8">
                {/* Featured badge */}
                <div className="absolute top-6 right-6">
                  <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0 px-3 py-1">
                    <Star className="w-3 h-3 mr-1" />
                    Featured Author
                  </Badge>
                </div>

                <div className="flex flex-col md:flex-row gap-6">
                  {/* Author Avatar */}
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <Avatar className="h-32 w-32 ring-4 ring-primary/20">
                        <AvatarImage src={featured.avatar} />
                        <AvatarFallback className="text-2xl">
                          {featured.first_name?.charAt(0)}
                          {featured.last_name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-full p-2">
                        <Award className="w-4 h-4" />
                      </div>
                    </div>
                  </div>

                  {/* Author Info */}
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold mb-2">
                      {featured.first_name} {featured.last_name}
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      {featured.bio || "Passionate writer and thought leader in technology and innovation."}
                    </p>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary">
                          {featured.articleCount || (featured.id ? (featured.id * 3 + 20) : 25)}
                        </div>
                        <div className="text-sm text-muted-foreground">Articles</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary">
                          {featured.followers || (featured.id ? (featured.id * 1000 + 5000) : 8500)}
                        </div>
                        <div className="text-sm text-muted-foreground">Followers</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary">
                          {featured.rating || "4.9"}
                        </div>
                        <div className="text-sm text-muted-foreground">Rating</div>
                      </div>
                    </div>

                    {/* Quote */}
                    <div className="bg-muted/50 rounded-lg p-4 mb-6 relative">
                      <Quote className="absolute top-2 left-2 w-4 h-4 text-muted-foreground" />
                      <p className="text-sm italic pl-6">
                        "{featured.quote || "Writing is not just about sharing knowledge, it's about inspiring others to think differently and push boundaries."}"
                      </p>
                    </div>

                    {/* Specialties */}
                    <div className="mb-6">
                      <p className="text-sm font-medium mb-2">Specializes in:</p>
                      <div className="flex flex-wrap gap-2">
                        {(featured.specialties || ['AI', 'Technology', 'Innovation']).map((specialty) => (
                          <Badge key={specialty} variant="outline" className="text-xs">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Action buttons */}
                    <div className="flex gap-3">
                      <Link href={`/author/${featured.first_name?.toLowerCase()}-${featured.last_name?.toLowerCase()}`}>
                        <Button className="btn-modern">
                          View Profile
                          <ArrowUpRight className="w-4 h-4 ml-2" />
                        </Button>
                      </Link>
                      <Button variant="outline" className="btn-modern">
                        Follow
                        <Users className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Other Featured Authors */}
          <div className="space-y-6">
            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-xl font-semibold mb-4"
            >
              More Amazing Authors
            </motion.h3>
            
            {otherAuthors.map((author, index) => (
              <motion.div
                key={author.id}
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm group">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12 ring-2 ring-transparent group-hover:ring-primary/20 transition-all duration-300">
                        <AvatarImage src={author.avatar} />
                        <AvatarFallback>
                          {author.first_name?.charAt(0)}
                          {author.last_name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold truncate group-hover:text-primary transition-colors">
                          {author.first_name} {author.last_name}
                        </h4>
                        <p className="text-sm text-muted-foreground truncate">
                          {author.bio || "Content Creator & Writer"}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <BookOpen className="w-3 h-3" />
                            {author.articleCount || (author.id ? (author.id * 2 + 10) : 15)} articles
                          </span>
                          <span className="flex items-center gap-1">
                            <Star className="w-3 h-3" />
                            {author.rating || "4.8"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* View all authors */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <Link href="/authors">
                <Button variant="outline" className="w-full btn-modern">
                  View All Authors
                  <ArrowUpRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.section>
  );
}
