import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Link from "next/link";
import LazyLoadingImage from "@/components/blocks/LazyLoadingImage";
import { motion } from "motion/react";
import { Clock, Eye, ArrowUpRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function BlogsList({ blogs }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
      {blogs.map((blog, index) => (
        <motion.div
          key={blog.id}
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          viewport={{ once: true }}
          whileHover={{ y: -8 }}
          className="group"
        >
          <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
            <div className="relative overflow-hidden">
              <LazyLoadingImage
                src={blog.image}
                alt={blog.title}
                className="h-48 w-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              {/* Floating stats */}
              <div className="absolute top-4 right-4 flex gap-2">
                <Badge variant="secondary" className="bg-black/70 text-white border-0 backdrop-blur-sm">
                  <Eye className="w-3 h-3 mr-1" />
                  {blog?.views}
                </Badge>
              </div>

              {/* Category badge */}
              <div className="absolute top-4 left-4">
                <Badge variant="default" className="bg-primary/90 hover:bg-primary text-primary-foreground border-0 backdrop-blur-sm">
                  {blog.category}
                </Badge>
              </div>

              {/* Read more button - appears on hover */}
              <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                <Button size="sm" variant="secondary" className="bg-white/90 text-black hover:bg-white">
                  Read More
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </Button>
              </div>
            </div>

            <CardHeader className="space-y-3 pb-4">
              <CardTitle className="text-lg line-clamp-2 leading-tight group-hover:text-primary transition-colors duration-300">
                {blog.title}
              </CardTitle>
              <CardDescription className="line-clamp-3 text-sm leading-relaxed">
                {blog.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Reading time and date */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {blog.readTime}
                </span>
                <span>{blog.publishedAt}</span>
              </div>

              {/* Author info */}
              <Link
                href={`/author/${blog?.author?.first_name?.toLowerCase()}-${blog?.author?.last_name?.toLowerCase()}`}
                className="flex items-center gap-3 hover:text-primary transition-colors duration-300 group/author"
              >
                <Avatar className="h-8 w-8 ring-2 ring-transparent group-hover/author:ring-primary/20 transition-all duration-300">
                  <AvatarImage
                    src={blog?.author?.avatar}
                    alt={`${blog?.author?.first_name} ${blog?.author?.last_name}`}
                  />
                  <AvatarFallback className="text-xs">
                    {blog?.author?.first_name?.charAt(0)?.toUpperCase()}
                    {blog?.author?.last_name?.charAt(0)?.toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {blog?.author?.first_name} {blog?.author?.last_name}
                  </p>
                  <p className="text-xs text-muted-foreground">Author</p>
                </div>
              </Link>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

