import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { ChevronLeft, ChevronRight, Clock, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { BLOGS } from '@/constants/blogs'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage'

export function HeroCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [featuredArticles, setFeaturedArticles] = useState([])

  useEffect(() => {
    if (BLOGS?.length > 0 && featuredArticles.length === 0) {
      setFeaturedArticles(BLOGS.slice(0, 5))
    }
  }, [featuredArticles.length])

  useEffect(() => {
    if (featuredArticles.length === 0) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % featuredArticles.length)
    }, 10000)

    return () => clearInterval(interval)
  }, [featuredArticles])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredArticles.length)
  }

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + featuredArticles.length) % featuredArticles.length
    )
  }

  const slide = featuredArticles[currentSlide]

  if (!slide) return null // Or a fallback skeleton/loading state

  return (
    <section className="relative h-[80dvh] w-full overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={slide.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="absolute inset-0"
        >
          <LazyLoadingImage
            src={slide.image}
            alt={slide.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/70" />

          <div className="absolute inset-0 flex items-center justify-center">
            <div className="flex items-center justify-center px-4">
              <div className="max-w-2xl text-white space-y-6">
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <Badge variant={'secondary'} className={'px-4'}>
                    {slide.category}
                  </Badge>
                </motion.div>

                <motion.h1
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-3xl md:text-5xl lg:text-6xl font-bold leading-tight"
                >
                  {slide.title}
                </motion.h1>

                <motion.p
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-lg text-gray-200"
                >
                  {slide.description}
                </motion.p>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center md:flex-row flex-col gap-4 text-sm text-gray-300"
                >
                  <Button variant={'link'} className={'text-white font-semibold flex items-center'}>
                    <Avatar>
                      <AvatarImage
                        src={slide?.author?.avatar}
                        alt={`${slide?.author?.first_name} ${slide?.author?.last_name}`}
                      />
                      <AvatarFallback>
                        {slide?.author?.first_name?.charAt(0)?.toUpperCase()}
                        {slide?.author?.last_name?.charAt(0)?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <Link
                      href={`/author/${slide?.author?.first_name?.toLowerCase()}-${slide?.author?.last_name?.toLowerCase()}`}
                      className='flex items-center'
                    >
                      {slide?.author?.first_name} {slide?.author?.last_name}
                    </Link>
                  </Button>
                  <div className='flex items-center gap-4'>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {slide.readTime}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {slide.views}
                    </span>
                    <span>{slide.publishedAt}</span>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className='md:text-left text-center'
                >
                  <Link href={`/blog/${slide.slug}`}>
                    <Button
                      size="lg"
                      className="bg-white/10 text-white border-white/30 hover:bg-white/20 hover:border-white/50 hover:scale-105 transition-all duration-300 backdrop-blur-md shadow-lg"
                    >
                      Read Full Article
                    </Button>
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Navigation Arrows */}
      <Button
        variant="ghost"
        size="icon"
        onClick={prevSlide}
        className="md:flex absolute hidden left-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 backdrop-blur-md shadow-lg transition-all duration-300 hover:scale-110"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-6 h-6" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={nextSlide}
        className="md:flex absolute hidden right-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 backdrop-blur-md shadow-lg transition-all duration-300 hover:scale-110"
        aria-label="Next slide"
      >
        <ChevronRight className="w-6 h-6" />
      </Button>

    </section>
  )
};
