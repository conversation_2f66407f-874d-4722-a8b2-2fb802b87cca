'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Search, Filter, X, Clock, TrendingUp, Calendar, Tag, SortAsc, SortDesc } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { categories } from '@/constants/categories';

export function AdvancedFilter({ onFilterChange, totalResults = 0 }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);
  const [readTimeRange, setReadTimeRange] = useState([0, 30]);
  const [sortBy, setSortBy] = useState('latest');
  const [dateRange, setDateRange] = useState('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Available tags (in a real app, these would come from your data)
  const availableTags = [
    'Machine Learning', 'Startup', 'Remote Work', 'Cryptocurrency', 
    'Web Development', 'Mobile Apps', 'Data Science', 'UX Design',
    'Marketing', 'Productivity', 'Health Tech', 'Fintech'
  ];

  const sortOptions = [
    { value: 'latest', label: 'Latest First', icon: Calendar },
    { value: 'oldest', label: 'Oldest First', icon: Calendar },
    { value: 'popular', label: 'Most Popular', icon: TrendingUp },
    { value: 'shortest', label: 'Quick Reads', icon: Clock },
    { value: 'longest', label: 'Deep Dives', icon: Clock },
  ];

  const dateOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'year', label: 'This Year' },
  ];

  // Apply filters whenever any filter changes
  useEffect(() => {
    const filters = {
      searchQuery,
      categories: selectedCategories,
      tags: selectedTags,
      readTimeRange,
      sortBy,
      dateRange
    };
    onFilterChange?.(filters);
  }, [searchQuery, selectedCategories, selectedTags, readTimeRange, sortBy, dateRange, onFilterChange]);

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handleTagToggle = (tag) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedCategories([]);
    setSelectedTags([]);
    setReadTimeRange([0, 30]);
    setSortBy('latest');
    setDateRange('all');
  };

  const activeFiltersCount = selectedCategories.length + selectedTags.length + 
    (searchQuery ? 1 : 0) + (dateRange !== 'all' ? 1 : 0) + 
    (readTimeRange[0] > 0 || readTimeRange[1] < 30 ? 1 : 0);

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative"
      >
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
        <Input
          placeholder="Search articles, topics, or authors..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-12 pr-4 py-6 text-lg bg-background/50 backdrop-blur-sm border-2 border-border/50 focus:border-primary/50 rounded-2xl"
        />
      </motion.div>

      {/* Filter Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex flex-wrap items-center gap-4"
      >
        {/* Advanced Filters Toggle */}
        <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <PopoverTrigger asChild>
            <Button 
              variant="outline" 
              className="relative btn-modern"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              {activeFiltersCount > 0 && (
                <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 text-xs">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-96 p-6 glass" align="start">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-lg">Advanced Filters</h3>
                {activeFiltersCount > 0 && (
                  <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                    Clear All
                  </Button>
                )}
              </div>

              {/* Categories */}
              <div>
                <label className="text-sm font-medium mb-3 block">Categories</label>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Badge
                      key={category}
                      variant={selectedCategories.includes(category) ? "default" : "outline"}
                      className="cursor-pointer hover:scale-105 transition-transform"
                      onClick={() => handleCategoryToggle(category)}
                    >
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="text-sm font-medium mb-3 block">Tags</label>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer hover:scale-105 transition-transform text-xs"
                      onClick={() => handleTagToggle(tag)}
                    >
                      <Tag className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Reading Time */}
              <div>
                <label className="text-sm font-medium mb-3 block">
                  Reading Time: {readTimeRange[0]}-{readTimeRange[1]} min
                </label>
                <Slider
                  value={readTimeRange}
                  onValueChange={setReadTimeRange}
                  max={30}
                  min={0}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Date Range */}
              <div>
                <label className="text-sm font-medium mb-3 block">Published</label>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {dateOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Sort By */}
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center gap-2">
                  <option.icon className="w-4 h-4" />
                  {option.label}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Results Count */}
        <div className="text-sm text-muted-foreground ml-auto">
          {totalResults} articles found
        </div>
      </motion.div>

      {/* Active Filters Display */}
      <AnimatePresence>
        {activeFiltersCount > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="flex flex-wrap gap-2"
          >
            {searchQuery && (
              <Badge variant="secondary" className="gap-1">
                Search: "{searchQuery}"
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 w-4 h-4 hover:text-destructive"
                  onClick={() => setSearchQuery('')}
                  aria-label="Remove search filter"
                >
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            )}
            {selectedCategories.map((category) => (
              <Badge key={category} variant="secondary" className="gap-1">
                {category}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 w-4 h-4 hover:text-destructive"
                  onClick={() => handleCategoryToggle(category)}
                  aria-label={`Remove ${category} filter`}
                >
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            ))}
            {selectedTags.map((tag) => (
              <Badge key={tag} variant="secondary" className="gap-1">
                <Tag className="w-3 h-3" />
                {tag}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 w-4 h-4 hover:text-destructive"
                  onClick={() => handleTagToggle(tag)}
                  aria-label={`Remove ${tag} tag filter`}
                >
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            ))}
            {dateRange !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                {dateOptions.find(d => d.value === dateRange)?.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 w-4 h-4 hover:text-destructive"
                  onClick={() => setDateRange('all')}
                  aria-label="Remove date filter"
                >
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
