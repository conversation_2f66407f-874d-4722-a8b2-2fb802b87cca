'use client';

import { motion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { TrendingUp, Eye, Clock, Flame } from 'lucide-react';
import Link from 'next/link';
import LazyLoadingImage from '@/components/blocks/LazyLoadingImage';

export function TrendingArticles({ articles }) {
  if (!articles || articles.length === 0) return null;

  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="section-modern bg-gradient-to-br from-orange-50/50 via-background to-red-50/50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20"
    >
      <div className="container-modern">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center justify-center gap-2 mb-4"
          >
            <Flame className="w-6 h-6 text-orange-500" />
            <h2 className="text-headline text-shimmer">Trending Now</h2>
            <Flame className="w-6 h-6 text-orange-500" />
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-muted-foreground text-lg max-w-2xl mx-auto"
          >
            The most popular articles that everyone's talking about
          </motion.p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {articles.slice(0, 6).map((article, index) => (
            <motion.div
              key={article.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -8 }}
              className="group"
            >
              <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
                <div className="relative overflow-hidden">
                  <LazyLoadingImage
                    src={article.image}
                    alt={article.title}
                    className="h-48 w-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Trending rank */}
                  <div className="absolute top-4 left-4">
                    <div className="flex items-center gap-1 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                      <TrendingUp className="w-3 h-3" />
                      #{index + 1}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="absolute top-4 right-4 flex gap-2">
                    <Badge variant="secondary" className="bg-black/70 text-white border-0 backdrop-blur-sm text-xs">
                      <Eye className="w-3 h-3 mr-1" />
                      {article.views}
                    </Badge>
                  </div>

                  {/* Trending indicator */}
                  <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                      <Flame className="w-3 h-3" />
                      Hot
                    </div>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="text-xs">
                      {article.category}
                    </Badge>
                    <span className="text-xs text-muted-foreground flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {article.readTime}
                    </span>
                  </div>

                  <h3 className="font-semibold text-lg line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-300">
                    {article.title}
                  </h3>

                  <p className="text-muted-foreground text-sm line-clamp-3 mb-4 leading-relaxed">
                    {article.description}
                  </p>

                  {/* Author and engagement */}
                  <div className="flex items-center justify-between">
                    <Link
                      href={`/author/${article.author?.first_name?.toLowerCase()}-${article.author?.last_name?.toLowerCase()}`}
                      className="flex items-center gap-2 hover:text-primary transition-colors duration-300 group/author"
                    >
                      <Avatar className="h-7 w-7 ring-2 ring-transparent group-hover/author:ring-primary/20 transition-all duration-300">
                        <AvatarImage src={article.author?.avatar} />
                        <AvatarFallback className="text-xs">
                          {article.author?.first_name?.charAt(0)}
                          {article.author?.last_name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">
                          {article.author?.first_name} {article.author?.last_name}
                        </p>
                        <p className="text-xs text-muted-foreground">{article.publishedAt}</p>
                      </div>
                    </Link>

                    {/* Trending score */}
                    <div className="text-right">
                      <div className="text-sm font-bold text-orange-500">
                        {((article.id * 7 + index * 3) % 50) + 50}%
                      </div>
                      <div className="text-xs text-muted-foreground">trending</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* View all trending button */}
        {/*
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link href="/trending">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-3 rounded-full font-medium hover:shadow-lg transition-all duration-300 flex items-center gap-2 mx-auto"
            >
              <TrendingUp className="w-4 h-4" />
              View All Trending Articles
            </motion.button>
          </Link>
        </motion.div>
        */}
      </div>
    </motion.section>
  );
}
