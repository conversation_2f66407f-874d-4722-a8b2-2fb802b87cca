import { useState } from 'react';
import { categories } from '@/constants/categories';
import { Button } from '@/components/ui/button';

export function CategoryFilter({ onCategoryChange, activeCategory = 'All' }) {
  const [selected, setSelected] = useState(activeCategory);

  const handleCategoryChange = (category) => {
    setSelected(category);
    onCategoryChange?.(category);
  };

  return (
    <div className="flex flex-wrap gap-4 justify-center">
      {categories.map((category) => (
        <Button
          key={category}
          variant={selected === category ? 'default' : 'outline'}
          className={`rounded-full px-8 py-3 text-sm font-medium transition-all duration-300 transform hover:scale-105 ${selected === category
            ? 'bg-foreground text-background shadow-lg border-0  hover:bg-foreground/'
            : 'border-2 hover:bg-foreground/5 hover:border-foreground/30 hover:text-foreground'
            }`}
          onClick={() => handleCategoryChange(category)}
        >
          {category}
        </Button>
      ))}
    </div>
  );
}
