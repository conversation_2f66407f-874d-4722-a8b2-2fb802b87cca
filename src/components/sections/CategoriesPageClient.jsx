'use client';

import { motion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowUpRight, Users, BookOpen, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import { CATEGORY_CONTENT } from '@/constants/categoryContent';

export function CategoriesPageClient() {
  const categories = Object.entries(CATEGORY_CONTENT);

  return (
    <>
      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10"
      >
        <div className="container-modern text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h1 className="text-display mb-6 text-shimmer">
              Explore Categories
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Dive deep into your areas of interest. From cutting-edge AI to lifestyle tips, 
              we've organized our content to help you find exactly what you're looking for.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                <BookOpen className="w-4 h-4" />
                {categories.reduce((total, [, data]) => total + data.stats.articles, 0)} Total Articles
              </span>
              <span className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                {categories.length} Categories
              </span>
              <span className="flex items-center gap-1">
                <TrendingUp className="w-4 h-4" />
                Updated Daily
              </span>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Categories Grid */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern"
      >
        <div className="container-modern">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {categories.map(([slug, data], index) => (
              <motion.div
                key={slug}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Link href={`/category/${slug}`}>
                  <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm cursor-pointer">
                    {/* Category Header */}
                    <div className={`h-32 bg-gradient-to-br ${data.color} relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-black/20" />
                      <div className="absolute top-4 left-4 text-4xl">
                        {data.icon}
                      </div>
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                          <ArrowUpRight className="w-5 h-5 text-white" />
                        </div>
                      </div>
                      <div className="absolute bottom-4 left-4 right-4">
                        <h3 className="text-xl font-bold text-white mb-1 group-hover:scale-105 transition-transform duration-300">
                          {data.title}
                        </h3>
                      </div>
                    </div>

                    <CardContent className="p-6">
                      <p className="text-muted-foreground mb-6 line-clamp-3 leading-relaxed">
                        {data.description}
                      </p>

                      {/* Stats */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">
                            {data.stats.articles}
                          </div>
                          <div className="text-xs text-muted-foreground">Articles</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">
                            {data.stats.readers}
                          </div>
                          <div className="text-xs text-muted-foreground">Readers</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">
                            {data.stats.experts}
                          </div>
                          <div className="text-xs text-muted-foreground">Experts</div>
                        </div>
                      </div>

                      {/* Popular Topics */}
                      <div className="mb-6">
                        <p className="text-sm font-medium mb-3">Popular Topics:</p>
                        <div className="flex flex-wrap gap-2">
                          {data.topics.slice(0, 3).map((topic) => (
                            <Badge 
                              key={topic} 
                              variant="secondary" 
                              className="text-xs"
                            >
                              {topic}
                            </Badge>
                          ))}
                          {data.topics.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{data.topics.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* CTA */}
                      <Button 
                        className="w-full btn-modern group-hover:scale-105 transition-transform duration-300"
                        variant="outline"
                      >
                        Explore {data.title}
                        <ArrowUpRight className="w-4 h-4 ml-2" />
                      </Button>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Featured Categories */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern bg-muted/30"
      >
        <div className="container-modern">
          <div className="text-center mb-12">
            <h2 className="text-headline mb-4">Most Popular Categories</h2>
            <p className="text-muted-foreground text-lg">
              The categories our readers love the most
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {categories
              .sort((a, b) => parseInt(b[1].stats.readers) - parseInt(a[1].stats.readers))
              .slice(0, 3)
              .map(([slug, data], index) => (
                <motion.div
                  key={slug}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <Link href={`/category/${slug}`}>
                    <div className="group cursor-pointer">
                      <div className={`w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${data.color} flex items-center justify-center text-3xl group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                        {data.icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                        {data.title}
                      </h3>
                      <p className="text-muted-foreground text-sm mb-3">
                        {data.stats.readers} readers
                      </p>
                      <Badge variant="outline" className="group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        #{index + 1} Most Popular
                      </Badge>
                    </div>
                  </Link>
                </motion.div>
              ))}
          </div>
        </div>
      </motion.section>
    </>
  );
}
