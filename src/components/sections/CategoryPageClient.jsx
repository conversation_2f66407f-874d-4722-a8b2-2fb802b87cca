'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  ArrowLeft, 
  TrendingUp, 
  Filter,
  Search,
  Clock,
  Eye
} from 'lucide-react';
import Link from 'next/link';
import { BLOGS } from '@/constants/blogs';

export function CategoryPageClient({ categoryData, slug }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedFormat, setSelectedFormat] = useState('All');
  const [selectedAuthor, setSelectedAuthor] = useState('All Authors');
  
  const categoryBlogs = BLOGS.filter(blog => 
    blog.category.toLowerCase() === slug?.toLowerCase()
  );

  // Get unique authors from category blogs
  const categoryAuthors = [...new Set(categoryBlogs.map(blog => `${blog.author.first_name} ${blog.author.last_name}`))];
  
  // Available tags for this category
  const availableTags = categoryData?.topics || [];

  // Filter blogs based on search and filters
  const filteredBlogs = categoryBlogs.filter(blog => {
    const authorName = `${blog.author.first_name} ${blog.author.last_name}`;
    const matchesSearch = searchQuery === '' || 
      blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.some(tag => blog.tags?.includes(tag));
    
    const matchesAuthor = selectedAuthor === 'All Authors' || authorName === selectedAuthor;
    
    return matchesSearch && matchesTags && matchesAuthor;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center gap-3 mb-6">
            <Link href="/categories" className="flex items-center text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4 mr-1" />
              {categoryData.title}
            </Link>
            <span className="text-gray-400">•</span>
            <span className="text-sm text-gray-600">{filteredBlogs.length} articles</span>
          </div>

          {/* Category Header Card */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xl font-bold">
                {categoryData.icon}
              </div>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">{categoryData.title}</h1>
                <p className="text-gray-600 mb-4">{categoryData.description}</p>
                <div className="flex items-center gap-6 text-sm text-gray-500">
                  <span>{categoryData.stats.articles} articles</span>
                  <span>{categoryData.stats.readers} followers</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Filters */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border p-6 sticky top-6">
              {/* Filter & Search Header */}
              <div className="flex items-center gap-2 mb-6">
                <Filter className="w-4 h-4 text-gray-500" />
                <span className="font-medium text-gray-900">Filter & Search</span>
              </div>

              {/* Search */}
              <div className="mb-6">
                <div className="relative">
                  <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search within this category"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 border-gray-200"
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-3">TAGS</h3>
                <div className="flex flex-wrap gap-2">
                  {availableTags.slice(0, 8).map((tag) => (
                    <button
                      key={tag}
                      onClick={() => {
                        setSelectedTags(prev => 
                          prev.includes(tag) 
                            ? prev.filter(t => t !== tag)
                            : [...prev, tag]
                        );
                      }}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        selectedTags.includes(tag)
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'bg-gray-50 text-gray-600 border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>

              {/* Format Filter */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-3">Format</h3>
                <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All</SelectItem>
                    <SelectItem value="Article">Article</SelectItem>
                    <SelectItem value="Tutorial">Tutorial</SelectItem>
                    <SelectItem value="Guide">Guide</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Author Filter */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-3">Author</h3>
                <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Authors">All Authors</SelectItem>
                    {categoryAuthors.map((author) => (
                      <SelectItem key={author} value={author}>{author}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Main Content - Articles */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              {filteredBlogs.map((blog, index) => (
                <motion.div
                  key={blog.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link href={`/blog/${blog.slug}`}>
                    <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group">
                      <div className="flex">
                        <div className="w-48 h-32 flex-shrink-0">
                          <img
                            src={blog.image}
                            alt={blog.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div className="flex-1 p-6">
                          <div className="flex items-start justify-between mb-2">
                            <Badge variant="secondary" className="text-xs">
                              Featured
                            </Badge>
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            {blog.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                            {blog.description}
                          </p>
                          <div className="flex flex-wrap gap-2 mb-4">
                            {availableTags.slice(0, 2).map((tag) => (
                              <span key={tag} className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="w-6 h-6">
                                <AvatarImage src={blog.author.avatar} />
                                <AvatarFallback className="text-xs">
                                  {blog.author.first_name[0]}{blog.author.last_name[0]}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm text-gray-600">{blog.author.first_name} {blog.author.last_name}</span>
                              <span className="text-gray-400">•</span>
                              <span className="text-sm text-gray-500">{blog.publishedAt}</span>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {blog.readTime}
                              </span>
                              <span className="flex items-center gap-1">
                                <Eye className="w-3 h-3" />
                                {blog.views}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              {/* Top Voices */}
              <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center gap-2 mb-4">
                  <TrendingUp className="w-4 h-4 text-gray-500" />
                  <span className="font-medium text-gray-900">Top Voices</span>
                </div>
                <div className="space-y-4">
                  {categoryData.featuredExperts.map((expert, index) => (
                    <div key={expert.name} className="flex items-center gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={expert.avatar} />
                        <AvatarFallback className="text-xs">
                          {expert.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 text-sm">{expert.name}</p>
                        <p className="text-xs text-gray-500 truncate">{expert.role}</p>
                        <p className="text-xs text-gray-400">{expert.expertise}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Free Guide CTA */}
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-6 text-white">
                <h3 className="font-bold text-lg mb-2">Free AI Development Guide</h3>
                <p className="text-blue-100 text-sm mb-4">
                  Get our comprehensive guide to building AI applications with modern frameworks and tools.
                </p>
                <Button className="w-full bg-white text-blue-600 hover:bg-blue-50">
                  Download Guide
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
