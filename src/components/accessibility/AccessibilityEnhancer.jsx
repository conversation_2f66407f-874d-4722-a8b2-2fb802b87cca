'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Eye,
  Contrast,
  MousePointer,
  Settings,
  X
} from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';

export function AccessibilityEnhancer() {
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [settings, setSettings] = useState({
    fontSize: 100,
    contrast: 'normal',
    reducedMotion: false,
    focusVisible: true,
    screenReader: false
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    // Load saved accessibility settings
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }

    // Apply initial settings
    applyAccessibilitySettings(settings);
  }, [isClient]);

  const applyAccessibilitySettings = (newSettings) => {
    if (!isClient) return;
    const root = document.documentElement;

    // Font size adjustment
    root.style.setProperty('--font-scale', `${newSettings.fontSize / 100}`);

    // Contrast adjustment
    switch (newSettings.contrast) {
      case 'high':
        root.classList.add('high-contrast');
        root.classList.remove('low-contrast');
        break;
      case 'low':
        root.classList.add('low-contrast');
        root.classList.remove('high-contrast');
        break;
      default:
        root.classList.remove('high-contrast', 'low-contrast');
    }

    // Reduced motion
    if (newSettings.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Enhanced focus visibility
    if (newSettings.focusVisible) {
      root.classList.add('enhanced-focus');
    } else {
      root.classList.remove('enhanced-focus');
    }

    // Save settings
    if (isClient) {
      localStorage.setItem('accessibility-settings', JSON.stringify(newSettings));
    }
  };

  const updateSetting = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    applyAccessibilitySettings(newSettings);
  };

  const resetSettings = () => {
    const defaultSettings = {
      fontSize: 100,
      contrast: 'normal',
      reducedMotion: false,
      focusVisible: true,
      screenReader: false
    };
    setSettings(defaultSettings);
    applyAccessibilitySettings(defaultSettings);
  };

  // Skip to main content link
  const SkipToMain = () => (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 font-medium"
    >
      Skip to main content
    </a>
  );

  // Keyboard navigation helper
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Alt + A to open accessibility menu
      if (e.altKey && e.key === 'a') {
        e.preventDefault();
        setIsOpen(!isOpen);
      }

      // Escape to close accessibility menu
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  if (!isClient) {
    return <SkipToMain />;
  }

  return (
    <>
      <SkipToMain />

      {/* Accessibility Menu Toggle */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: 2 }}
        className="md:flex hidden fixed bottom-6 left-6 z-50"
      >
        <Button
          onClick={() => setIsOpen(!isOpen)}
          className="w-14 h-14 rounded-full shadow-lg bg-primary hover:bg-primary/90 text-primary-foreground"
          aria-label="Open accessibility settings (Alt + A)"
          title="Accessibility Settings"
        >
          <Settings className="w-6 h-6" />
        </Button>
      </motion.div>

      {/* Accessibility Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: -300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -300 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-24 left-6 z-50 w-80"
          >
            <Card className="shadow-2xl border-2 border-primary/20">
              <CardContent>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold">Accessibility Settings</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    aria-label="Close accessibility settings"
                  >
                    <X size={18} />
                  </Button>
                </div>

                <div className="space-y-6">
                  {/* Contrast */}
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium mb-3">
                      <Contrast className="w-4 h-4" />
                      Contrast
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      {['normal', 'low'].map((contrast) => (
                        <Button
                          key={contrast}
                          variant={settings.contrast === contrast ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateSetting('contrast', contrast)}
                          className="capitalize"
                        >
                          {contrast}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Motion */}
                  <div>
                    <label className="flex items-center justify-between">
                      <span className="flex items-center gap-2 text-sm font-medium">
                        <MousePointer className="w-4 h-4" />
                        Reduce Motion
                      </span>
                      <Button
                        variant={settings.reducedMotion ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateSetting('reducedMotion', !settings.reducedMotion)}
                      >
                        {settings.reducedMotion ? 'On' : 'Off'}
                      </Button>
                    </label>
                  </div>

                  {/* Focus Visibility */}
                  <div>
                    <label className="flex items-center justify-between">
                      <span className="flex items-center gap-2 text-sm font-medium">
                        <Eye className="w-4 h-4" />
                        Enhanced Focus
                      </span>
                      <Button
                        variant={settings.focusVisible ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateSetting('focusVisible', !settings.focusVisible)}
                      >
                        {settings.focusVisible ? 'On' : 'Off'}
                      </Button>
                    </label>
                  </div>

                  {/* Reset Button */}
                  <Button
                    variant="outline"
                    onClick={resetSettings}
                    className="w-full"
                  >
                    Reset to Defaults
                  </Button>

                  {/* Keyboard Shortcuts Info */}
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p className="font-medium">Keyboard Shortcuts:</p>
                    <p>Alt + A: Toggle this menu</p>
                    <p>Tab: Navigate elements</p>
                    <p>Enter/Space: Activate buttons</p>
                    <p>Esc: Close menus</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Accessibility CSS */}
      <style jsx global>{`
        /* Font scaling */
        html {
          font-size: calc(16px * var(--font-scale, 1));
        }

        /* High contrast mode */
        .high-contrast {
          --background: #000000;
          --foreground: #ffffff;
          --primary: #ffff00;
          --border: #ffffff;
        }

        .high-contrast img {
          filter: contrast(150%);
        }

        /* Low contrast mode */
        .low-contrast {
          filter: contrast(0.8);
        }

        /* Reduced motion */
        .reduce-motion *,
        .reduce-motion *::before,
        .reduce-motion *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }

        /* Enhanced focus visibility */
        .enhanced-focus *:focus {
          outline: 3px solid #ffff00 !important;
          outline-offset: 2px !important;
        }

        /* Screen reader only content */
        .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
        }

        .sr-only:focus {
          position: static;
          width: auto;
          height: auto;
          padding: inherit;
          margin: inherit;
          overflow: visible;
          clip: auto;
          white-space: normal;
        }

        /* Focus management */
        [tabindex="-1"]:focus {
          outline: none;
        }

        /* Ensure interactive elements are large enough */
        button, a, input, select, textarea {
          min-height: 44px;
          min-width: 44px;
        }
      `}</style>
    </>
  );
}
