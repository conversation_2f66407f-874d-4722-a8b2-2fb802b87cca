'use client';

import { useState, useEffect } from 'react';
import { motion, useScroll, useSpring } from 'motion/react';
import { BookOpen, Clock, CheckCircle } from 'lucide-react';

export function ReadingProgress({
  estimatedReadTime = 5,
  showTimeRemaining = true,
  showPercentage = true,
  className = ""
}) {
  const [readingProgress, setReadingProgress] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(estimatedReadTime);
  const [isClient, setIsClient] = useState(false);
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    const updateProgress = () => {
      const scrolled = window.scrollY;
      const maxHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min(scrolled / maxHeight, 1);
      
      setReadingProgress(progress);
      
      if (showTimeRemaining) {
        const remaining = Math.max(0, estimatedReadTime * (1 - progress));
        setTimeRemaining(Math.ceil(remaining));
      }
    };

    window.addEventListener('scroll', updateProgress);
    updateProgress();

    return () => window.removeEventListener('scroll', updateProgress);
  }, [estimatedReadTime, showTimeRemaining, isClient]);

  if (!isClient) {
    return null;
  }

  return (
    <>
      {/* Fixed progress bar at top */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-primary/20 z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          className="h-full bg-gradient-to-r from-primary to-primary/70"
          style={{ scaleX, transformOrigin: "0%" }}
        />
      </motion.div>

      {/* Floating progress indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 1 }}
        className={`fixed bottom-6 right-6 z-40 ${className}`}
      >
        <div className="bg-background/90 backdrop-blur-sm border border-border/50 rounded-2xl p-4 shadow-lg">
          <div className="flex items-center gap-3">
            {/* Progress circle */}
            <div className="relative w-12 h-12">
              <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  className="text-muted stroke-current"
                  strokeWidth="3"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <motion.path
                  className="text-primary stroke-current"
                  strokeWidth="3"
                  strokeLinecap="round"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: readingProgress }}
                  transition={{ duration: 0.3 }}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                {readingProgress >= 0.99 ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <BookOpen className="w-4 h-4 text-primary" />
                )}
              </div>
            </div>

            {/* Progress info */}
            <div className="text-sm">
              {showPercentage && (
                <div className="font-semibold text-foreground">
                  {Math.round(readingProgress * 100)}% read
                </div>
              )}
              {showTimeRemaining && (
                <div className="text-muted-foreground flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {timeRemaining > 0 ? `${timeRemaining} min left` : 'Complete!'}
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </>
  );
}

// Simplified version for article cards
export function ReadingProgressMini({ progress = 0, className = "" }) {
  return (
    <div className={`w-full bg-muted rounded-full h-1 ${className}`}>
      <motion.div
        className="h-full bg-gradient-to-r from-primary to-primary/70 rounded-full"
        initial={{ width: 0 }}
        animate={{ width: `${progress * 100}%` }}
        transition={{ duration: 0.5 }}
      />
    </div>
  );
}

// Reading time estimator
export function ReadingTimeEstimator({ content, wordsPerMinute = 200 }) {
  const wordCount = content ? content.split(/\s+/).length : 0;
  const readTime = Math.ceil(wordCount / wordsPerMinute);

  return (
    <div className="flex items-center gap-1 text-sm text-muted-foreground">
      <Clock className="w-4 h-4" />
      <span>{readTime} min read</span>
      <span className="text-xs">({wordCount} words)</span>
    </div>
  );
}
