'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Share2, 
  Twitter, 
  Facebook, 
  Linkedin, 
  Link2, 
  Mail, 
  MessageCircle,
  Copy,
  Check
} from 'lucide-react';
import { toast } from 'sonner';

export function SocialShare({
  url = '',
  title = '',
  description = '',
  hashtags = [],
  className = "",
  variant = "default" // "default", "floating", "minimal"
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const [currentUrl, setCurrentUrl] = useState(url);

  useEffect(() => {
    if (!url && typeof window !== 'undefined') {
      setCurrentUrl(window.location.href);
    }
  }, [url]);

  const shareData = {
    url: encodeURIComponent(currentUrl),
    title: encodeURIComponent(title),
    description: encodeURIComponent(description),
    hashtags: hashtags.join(',')
  };

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?url=${shareData.url}&text=${shareData.title}&hashtags=${shareData.hashtags}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${shareData.url}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${shareData.url}`,
    email: `mailto:?subject=${shareData.title}&body=${shareData.description}%0A%0A${shareData.url}`,
    whatsapp: `https://wa.me/?text=${shareData.title}%20${shareData.url}`
  };

  const handleShare = (platform) => {
    if (platform === 'copy') {
      navigator.clipboard.writeText(currentUrl).then(() => {
        setCopied(true);
        toast.success('Link copied to clipboard!');
        setTimeout(() => setCopied(false), 2000);
      });
    } else if (platform === 'native' && navigator.share) {
      navigator.share({
        title,
        text: description,
        url: currentUrl
      });
    } else {
      window.open(shareLinks[platform], '_blank', 'width=600,height=400');
    }
    setIsOpen(false);
  };

  const shareButtons = [
    { 
      platform: 'twitter', 
      icon: Twitter, 
      label: 'Twitter', 
      color: 'hover:bg-blue-500 hover:text-white',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20'
    },
    { 
      platform: 'facebook', 
      icon: Facebook, 
      label: 'Facebook', 
      color: 'hover:bg-blue-600 hover:text-white',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20'
    },
    { 
      platform: 'linkedin', 
      icon: Linkedin, 
      label: 'LinkedIn', 
      color: 'hover:bg-blue-700 hover:text-white',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20'
    },
    { 
      platform: 'whatsapp', 
      icon: MessageCircle, 
      label: 'WhatsApp', 
      color: 'hover:bg-green-500 hover:text-white',
      bgColor: 'bg-green-50 dark:bg-green-950/20'
    },
    { 
      platform: 'email', 
      icon: Mail, 
      label: 'Email', 
      color: 'hover:bg-gray-500 hover:text-white',
      bgColor: 'bg-gray-50 dark:bg-gray-950/20'
    },
    { 
      platform: 'copy', 
      icon: copied ? Check : Copy, 
      label: copied ? 'Copied!' : 'Copy Link', 
      color: copied ? 'bg-green-500 text-white' : 'hover:bg-gray-500 hover:text-white',
      bgColor: 'bg-gray-50 dark:bg-gray-950/20'
    }
  ];

  if (variant === "minimal") {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {shareButtons.slice(0, 3).map((button) => (
          <Button
            key={button.platform}
            variant="ghost"
            size="sm"
            onClick={() => handleShare(button.platform)}
            className={`p-2 ${button.color} transition-all duration-300`}
          >
            <button.icon className="w-4 h-4" />
          </Button>
        ))}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 hover:bg-muted transition-all duration-300"
        >
          <Share2 className="w-4 h-4" />
        </Button>
      </div>
    );
  }

  if (variant === "floating") {
    return (
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 1.5 }}
        className={`fixed right-6 top-1/2 -translate-y-1/2 z-40 ${className}`}
      >
        <div className="flex flex-col gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="bg-background/90 backdrop-blur-sm border-border/50 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Share2 className="w-4 h-4" />
          </Button>
          
          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.2 }}
                className="flex flex-col gap-2"
              >
                {shareButtons.map((button, index) => (
                  <motion.div
                    key={button.platform}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleShare(button.platform)}
                      className={`bg-background/90 backdrop-blur-sm border-border/50 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ${button.color}`}
                    >
                      <button.icon className="w-4 h-4" />
                    </Button>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    );
  }

  // Default variant
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2">
        <Share2 className="w-5 h-5 text-muted-foreground" />
        <span className="font-medium">Share this article</span>
      </div>
      
      <div className="flex flex-wrap gap-3">
        {shareButtons.map((button) => (
          <Button
            key={button.platform}
            variant="outline"
            size="sm"
            onClick={() => handleShare(button.platform)}
            className={`flex items-center gap-2 ${button.bgColor} ${button.color} transition-all duration-300 hover:scale-105`}
          >
            <button.icon className="w-4 h-4" />
            <span className="hidden sm:inline">{button.label}</span>
          </Button>
        ))}
      </div>

      {/* Native share button for mobile */}
      {typeof navigator !== 'undefined' && navigator.share && (
        <Button
          variant="outline"
          onClick={() => handleShare('native')}
          className="w-full sm:w-auto"
        >
          <Share2 className="w-4 h-4 mr-2" />
          Share via...
        </Button>
      )}
    </div>
  );
}

// Compact share counter component
export function ShareCounter({ shares = 0, className = "" }) {
  return (
    <div className={`flex items-center gap-1 text-sm text-muted-foreground ${className}`}>
      <Share2 className="w-4 h-4" />
      <span>{shares > 0 ? `${shares} shares` : 'Share'}</span>
    </div>
  );
}

// Quick share buttons for article cards
export function QuickShare({ url, title, className = "" }) {
  const handleQuickShare = (platform) => {
    const shareData = {
      url: encodeURIComponent(url),
      title: encodeURIComponent(title)
    };

    const links = {
      twitter: `https://twitter.com/intent/tweet?url=${shareData.url}&text=${shareData.title}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${shareData.url}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${shareData.url}`
    };

    window.open(links[platform], '_blank', 'width=600,height=400');
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleQuickShare('twitter')}
        className="p-1 h-auto hover:bg-blue-500 hover:text-white transition-all duration-300"
      >
        <Twitter className="w-3 h-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleQuickShare('facebook')}
        className="p-1 h-auto hover:bg-blue-600 hover:text-white transition-all duration-300"
      >
        <Facebook className="w-3 h-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleQuickShare('linkedin')}
        className="p-1 h-auto hover:bg-blue-700 hover:text-white transition-all duration-300"
      >
        <Linkedin className="w-3 h-3" />
      </Button>
    </div>
  );
}
