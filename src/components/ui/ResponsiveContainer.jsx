'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export function ResponsiveContainer({ 
  children, 
  className = "",
  maxWidth = "7xl",
  padding = "responsive"
}) {
  const [screenSize, setScreenSize] = useState('lg');

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 640) setScreenSize('sm');
      else if (width < 768) setScreenSize('md');
      else if (width < 1024) setScreenSize('lg');
      else if (width < 1280) setScreenSize('xl');
      else setScreenSize('2xl');
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  const maxWidthClasses = {
    'sm': 'max-w-sm',
    'md': 'max-w-md',
    'lg': 'max-w-lg',
    'xl': 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    'full': 'max-w-full'
  };

  const paddingClasses = {
    'none': '',
    'sm': 'px-4',
    'md': 'px-6',
    'lg': 'px-8',
    'responsive': 'px-4 sm:px-6 lg:px-8'
  };

  return (
    <div 
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
      data-screen-size={screenSize}
    >
      {children}
    </div>
  );
}

// Hook for responsive behavior
export function useResponsive() {
  const [breakpoint, setBreakpoint] = useState('lg');
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(true);

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width < 640) {
        setBreakpoint('sm');
        setIsMobile(true);
        setIsTablet(false);
        setIsDesktop(false);
      } else if (width < 768) {
        setBreakpoint('md');
        setIsMobile(true);
        setIsTablet(false);
        setIsDesktop(false);
      } else if (width < 1024) {
        setBreakpoint('lg');
        setIsMobile(false);
        setIsTablet(true);
        setIsDesktop(false);
      } else if (width < 1280) {
        setBreakpoint('xl');
        setIsMobile(false);
        setIsTablet(false);
        setIsDesktop(true);
      } else {
        setBreakpoint('2xl');
        setIsMobile(false);
        setIsTablet(false);
        setIsDesktop(true);
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return {
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen: isMobile || isTablet,
    isLargeScreen: isDesktop
  };
}

// Responsive grid component
export function ResponsiveGrid({ 
  children, 
  className = "",
  cols = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = "6"
}) {
  const gridClasses = `grid gap-${gap} grid-cols-${cols.sm} md:grid-cols-${cols.md} lg:grid-cols-${cols.lg} xl:grid-cols-${cols.xl}`;
  
  return (
    <div className={cn(gridClasses, className)}>
      {children}
    </div>
  );
}

// Responsive text component
export function ResponsiveText({ 
  children, 
  className = "",
  size = { sm: "sm", md: "base", lg: "lg" },
  weight = "normal"
}) {
  const textClasses = `text-${size.sm} md:text-${size.md} lg:text-${size.lg} font-${weight}`;
  
  return (
    <span className={cn(textClasses, className)}>
      {children}
    </span>
  );
}

// Responsive spacing component
export function ResponsiveSpacing({ 
  children, 
  className = "",
  padding = { sm: "4", md: "6", lg: "8" },
  margin = { sm: "0", md: "0", lg: "0" }
}) {
  const spacingClasses = `p-${padding.sm} md:p-${padding.md} lg:p-${padding.lg} m-${margin.sm} md:m-${margin.md} lg:m-${margin.lg}`;
  
  return (
    <div className={cn(spacingClasses, className)}>
      {children}
    </div>
  );
}

// Media query hook
export function useMediaQuery(query) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    
    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}

// Responsive image component with lazy loading
export function ResponsiveImage({ 
  src, 
  alt, 
  className = "",
  sizes = "100vw",
  priority = false,
  ...props 
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!priority) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      const imgElement = document.querySelector(`[data-src="${src}"]`);
      if (imgElement) {
        observer.observe(imgElement);
      }

      return () => observer.disconnect();
    } else {
      setIsInView(true);
    }
  }, [src, priority]);

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* Placeholder */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}
      
      {/* Actual image */}
      {(isInView || priority) && (
        <img
          src={src}
          alt={alt}
          sizes={sizes}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0"
          )}
          onLoad={() => setIsLoaded(true)}
          data-src={src}
          {...props}
        />
      )}
    </div>
  );
}

// Responsive video component
export function ResponsiveVideo({ 
  src, 
  poster, 
  className = "",
  autoPlay = false,
  muted = true,
  loop = false
}) {
  const { isMobile } = useResponsive();

  return (
    <div className={cn("relative w-full", className)}>
      <video
        src={src}
        poster={poster}
        autoPlay={autoPlay && !isMobile} // Don't autoplay on mobile to save data
        muted={muted}
        loop={loop}
        playsInline
        className="w-full h-auto"
        preload={isMobile ? "none" : "metadata"}
      />
    </div>
  );
}
