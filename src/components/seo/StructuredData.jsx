import { CompanyName } from "@/constants/companyName";
import { BaseUrl, SocialLinks } from "@/constants/url";

export function StructuredData({ type = "website", data = {} }) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type,
    };

    switch (type) {
      case "website":
        return {
          ...baseData,
          name: CompanyName,
          description: "Modern tech and culture blog featuring insights on AI, e-commerce, trading, productivity, and lifestyle.",
          url: "https://techculture.blog",
          publisher: {
            "@type": "Organization",
            name: CompanyName,
            logo: {
              "@type": "ImageObject",
              url: `${BaseUrl}/logo.png`
            }
          },
          potentialAction: {
            "@type": "SearchAction",
            target: `${BaseUrl}/search?q={search_term_string}`,
            "query-input": "required name=search_term_string"
          }
        };

      case "article":
        return {
          ...baseData,
          "@type": "BlogPosting",
          headline: data.title,
          description: data.description,
          image: data.image,
          author: {
            "@type": "Person",
            name: `${data.author?.first_name} ${data.author?.last_name}`,
            image: data.author?.avatar
          },
          publisher: {
            "@type": "Organization",
            name: CompanyName,
            logo: {
              "@type": "ImageObject",
              url: `${BaseUrl}/logo.png`
            }
          },
          datePublished: data.publishedAt,
          dateModified: data.updatedAt || data.publishedAt,
          mainEntityOfPage: {
            "@type": "WebPage",
            "@id": `${BaseUrl}/article/${data.slug}`
          },
          articleSection: data.category,
          wordCount: data.wordCount,
          timeRequired: data.readTime,
          keywords: data.tags?.join(", ")
        };

      case "organization":
        return {
          ...baseData,
          name: CompanyName,
          description: "Modern tech and culture blog",
          url: BaseUrl,
          logo: `${BaseUrl}/logo.png`,
          sameAs: SocialLinks,
          contactPoint: {
            "@type": "ContactPoint",
            contactType: "customer service",
            email: "<EMAIL>"
          }
        };

      case "breadcrumb":
        return {
          ...baseData,
          "@type": "BreadcrumbList",
          itemListElement: data.breadcrumbs?.map((crumb, index) => ({
            "@type": "ListItem",
            position: index + 1,
            name: crumb.name,
            item: crumb.url
          }))
        };

      default:
        return baseData;
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData())
      }}
    />
  );
}
