export function StructuredData({ type = "website", data = {} }) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type,
    };

    switch (type) {
      case "website":
        return {
          ...baseData,
          name: "TechCulture",
          description: "Modern tech and culture blog featuring insights on AI, e-commerce, trading, productivity, and lifestyle.",
          url: "https://techculture.blog",
          publisher: {
            "@type": "Organization",
            name: "TechCulture",
            logo: {
              "@type": "ImageObject",
              url: "https://techculture.blog/logo.png"
            }
          },
          potentialAction: {
            "@type": "SearchAction",
            target: "https://techculture.blog/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        };

      case "article":
        return {
          ...baseData,
          "@type": "BlogPosting",
          headline: data.title,
          description: data.description,
          image: data.image,
          author: {
            "@type": "Person",
            name: `${data.author?.first_name} ${data.author?.last_name}`,
            image: data.author?.avatar
          },
          publisher: {
            "@type": "Organization",
            name: "TechCulture",
            logo: {
              "@type": "ImageObject",
              url: "https://techculture.blog/logo.png"
            }
          },
          datePublished: data.publishedAt,
          dateModified: data.updatedAt || data.publishedAt,
          mainEntityOfPage: {
            "@type": "WebPage",
            "@id": `https://techculture.blog/article/${data.slug}`
          },
          articleSection: data.category,
          wordCount: data.wordCount,
          timeRequired: data.readTime,
          keywords: data.tags?.join(", ")
        };

      case "organization":
        return {
          ...baseData,
          name: "TechCulture",
          description: "Modern tech and culture blog",
          url: "https://techculture.blog",
          logo: "https://techculture.blog/logo.png",
          sameAs: [
            "https://twitter.com/techculture",
            "https://linkedin.com/company/techculture",
            "https://github.com/techculture"
          ],
          contactPoint: {
            "@type": "ContactPoint",
            contactType: "customer service",
            email: "<EMAIL>"
          }
        };

      case "breadcrumb":
        return {
          ...baseData,
          "@type": "BreadcrumbList",
          itemListElement: data.breadcrumbs?.map((crumb, index) => ({
            "@type": "ListItem",
            position: index + 1,
            name: crumb.name,
            item: crumb.url
          }))
        };

      default:
        return baseData;
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData())
      }}
    />
  );
}
