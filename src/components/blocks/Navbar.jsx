'use client';

import { useState } from 'react';
import { <PERSON>u, X, Zap } from 'lucide-react';
import Link from 'next/link';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { categories } from '@/constants/categories';
import { CompanyName } from '@/constants/companyName';
import { ModeToggle } from '../ui/mode-toggle';


export function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="sticky top-0 z-50 w-full glass border-b border-border/50">
      <div className="container-modern">
        <div className="flex h-20 items-center justify-between">
          {/* Logo */}
          <Link href="/home" className="flex items-center space-x-3 group">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/70 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-gradient-primary group-hover:scale-105 transition-transform duration-300">
              {CompanyName}
            </h1>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/home"
              className="hover:text-primary transition-all duration-300 text-sm font-medium relative group px-3 py-2 rounded-lg hover:bg-primary/5 flex items-center"
            >
              Home
              <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-8 transform -translate-x-1/2"></span>
            </Link>
            <Link
              href="/categories"
              className="hover:text-primary transition-all duration-300 text-sm font-medium relative group px-3 py-2 rounded-lg hover:bg-primary/5 flex items-center"
            >
              Categories
              <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-8 transform -translate-x-1/2"></span>
            </Link>
            <Link
              href="/about"
              className="hover:text-primary transition-all duration-300 text-sm font-medium relative group px-3 py-2 rounded-lg hover:bg-primary/5 flex items-center"
            >
              About
              <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-8 transform -translate-x-1/2"></span>
            </Link>
            {categories.slice(0, 4).map((category) => (
              <Link
                key={category}
                href={`/category/${category.toLowerCase()}`}
                className="hover:text-primary transition-all duration-300 text-sm font-medium relative group px-3 py-2 rounded-lg hover:bg-primary/5 flex items-center"
              >
                {category}
                <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-8 transform -translate-x-1/2"></span>
              </Link>
            ))}
          </nav>

          {/* Additional Icons */}
          <div className='flex items-center gap-3'>
            <ModeToggle />
            <div className="md:hidden">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" aria-label="Open mobile menu">
                    <Menu className="w-6 h-6" />
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader className='p-6'>
                    <SheetTitle className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <Zap className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-xl font-bold text-gradient-primary">
                        {CompanyName}
                      </span>
                    </SheetTitle>

                    <nav className="md:hidden flex flex-col gap-6 items-center mt-6">
                      {categories.map((category) => (
                        <Link
                          key={category}
                          href={`#${category.toLowerCase()}`}
                          className="hover:text-primary transition-colors duration-300 text-sm font-medium relative group"
                        >
                          {category}
                          <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </Link>
                      ))}
                    </nav>
                  </SheetHeader>
                </SheetContent>
              </Sheet>
            </div>
          </div>

        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-4 pb-6 space-y-2 border-t backdrop-blur-xl">
              {categories.map((category) => (
                <Link
                  key={category}
                  href={`#${category.toLowerCase()}`}
                  className="block px-4 py-3 hover:text-primary hover:bg-primary/5 transition-all duration-200 rounded-lg font-medium"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {category}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
