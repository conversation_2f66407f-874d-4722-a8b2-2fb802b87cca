export const metadata = {
  title: 'Categories | TechCulture',
  description: 'Explore all content categories on TechCulture. From AI and technology to lifestyle and productivity, find articles that match your interests.',
  keywords: 'categories, AI, technology, e-commerce, trading, productivity, lifestyle, blog categories',
};

import { StructuredData } from '@/components/seo/StructuredData';
import { CategoriesPageClient } from '@/components/sections/CategoriesPageClient';

export default function CategoriesPage() {
  return (
    <>
      <StructuredData 
        type="breadcrumb" 
        data={{
          breadcrumbs: [
            { name: "Home", url: "/home" },
            { name: "Categories", url: "/categories" }
          ]
        }}
      />
      <CategoriesPageClient />
    </>
  );
}
