import { getCategoryData } from '@/constants/categoryContent';

// Generate metadata for SEO
export async function generateMetadata({ params }) {
  const { slug } = await params;
  const categoryData = getCategoryData(slug);

  if (!categoryData) {
    return {
      title: 'Category Not Found | TechCulture',
      description: 'The category you are looking for does not exist.'
    };
  }

  return {
    title: `${categoryData.title} Articles | TechCulture`,
    description: categoryData.description,
    keywords: categoryData.topics.join(', '),
    openGraph: {
      title: `${categoryData.title} Articles | TechCulture`,
      description: categoryData.description,
      images: [categoryData.heroImage],
    },
  };
}
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { StructuredData } from '@/components/seo/StructuredData';
import { CategoryPageClient } from '@/components/sections/CategoryPageClient';

export default async function CategoryPage({ params }) {
  const { slug } = await params;
  const categoryData = getCategoryData(slug);

  if (!categoryData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Category Not Found</h1>
          <p className="text-muted-foreground mb-8">The category you're looking for doesn't exist.</p>
          <Link href="/home">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <StructuredData
        type="breadcrumb"
        data={{
          breadcrumbs: [
            { name: "Home", url: "/home" },
            { name: "Categories", url: "/categories" },
            { name: categoryData.title, url: `/category/${slug}` }
          ]
        }}
      />
      <CategoryPageClient categoryData={categoryData} slug={slug} />
    </>
  );
}
