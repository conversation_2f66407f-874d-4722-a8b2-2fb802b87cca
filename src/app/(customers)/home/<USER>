'use client';

import BlogsList from "@/components/sections/home/<USER>";
import { AdvancedFilter } from "@/components/sections/home/<USER>";
import { HeroCarousel } from "@/components/sections/home/<USER>";
import { Button } from "@/components/ui/button";
import { BLOGS } from "@/constants/blogs";
import { ArrowUpRight, TrendingUp, Star, Users, BookOpen } from "lucide-react";
import Link from "next/link";
import { useEffect, useState, useCallback } from "react";
import { motion } from "motion/react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { StructuredData } from "@/components/seo/StructuredData";
import { FeaturedPosts } from "@/components/sections/home/<USER>";
import { TrendingArticles } from "@/components/sections/home/<USER>";
import { AuthorSpotlight } from "@/components/sections/home/<USER>";
import { ReadingProgress } from "@/components/ui/ReadingProgress";
import { Newsletter } from "@/components/sections/Newsletter";
import Head from "next/head";

export default function page() {
  const [filteredBlogs, setFilteredBlogs] = useState(BLOGS);
  const [currentFilters, setCurrentFilters] = useState({});

  // Get featured posts (top 3 most viewed)
  const featuredPosts = BLOGS.sort((a, b) => parseFloat(b.views) - parseFloat(a.views)).slice(0, 3);

  // Get trending articles (deterministic selection for demo)
  const trendingArticles = BLOGS.sort((a, b) => (a.id * 7 + b.id * 3) % 2 - 1).slice(0, 6);

  // Get unique authors for author spotlight
  const uniqueAuthors = BLOGS.reduce((acc, blog) => {
    const authorKey = `${blog.author.first_name}-${blog.author.last_name}`;
    if (!acc.find(author => `${author.first_name}-${author.last_name}` === authorKey)) {
      acc.push(blog.author);
    }
    return acc;
  }, []).slice(0, 4);

  const handleFilterChange = useCallback((filters) => {
    setCurrentFilters(filters);

    let filtered = [...BLOGS];

    // Apply search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(query) ||
        blog.description.toLowerCase().includes(query) ||
        blog.category.toLowerCase().includes(query) ||
        `${blog.author.first_name} ${blog.author.last_name}`.toLowerCase().includes(query)
      );
    }

    // Apply category filters
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(blog => filters.categories.includes(blog.category));
    }

    // Apply reading time filter
    if (filters.readTimeRange) {
      filtered = filtered.filter(blog => {
        const readTime = parseInt(blog.readTime.replace(' min read', ''));
        return readTime >= filters.readTimeRange[0] && readTime <= filters.readTimeRange[1];
      });
    }

    // Apply date range filter
    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (filters.dateRange) {
        case 'today':
          filterDate.setDate(now.getDate() - 1);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter(blog => new Date(blog.publishedAt) >= filterDate);
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'latest':
        filtered.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.publishedAt) - new Date(b.publishedAt));
        break;
      case 'popular':
        filtered.sort((a, b) => parseFloat(b.views) - parseFloat(a.views));
        break;
      case 'shortest':
        filtered.sort((a, b) => parseInt(a.readTime) - parseInt(b.readTime));
        break;
      case 'longest':
        filtered.sort((a, b) => parseInt(b.readTime) - parseInt(a.readTime));
        break;
    }

    setFilteredBlogs(filtered);
  }, []);


  return (
    <>
      <StructuredData type="website" />
      <StructuredData type="organization" />
      <ReadingProgress estimatedReadTime={8} />

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="relative"
      >
        <HeroCarousel />
      </motion.div>

      {/* Featured Posts Section */}
      <FeaturedPosts posts={featuredPosts} />

      {/* Stats Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern bg-gradient-to-br from-background via-muted/30 to-background"
      >
        <div className="container-modern">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { icon: BookOpen, label: "Articles", value: "10K+", color: "text-blue-500" },
              { icon: Users, label: "Readers", value: "50K+", color: "text-green-500" },
              { icon: TrendingUp, label: "Growth", value: "200%", color: "text-purple-500" },
              { icon: Star, label: "Rating", value: "4.9", color: "text-primary" }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
                <div className="text-3xl font-bold text-gradient-primary mb-2">{stat.value}</div>
                <div className="text-muted-foreground font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Advanced Search & Filter */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern border-b glass"
      >
        <div className="container-modern">
          <div className="text-center mb-12">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-headline mb-4 text-shimmer"
            >
              Discover Your Perfect Read
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-muted-foreground text-lg max-w-2xl mx-auto"
            >
              Use our advanced search and filtering system to find exactly what you're looking for
            </motion.p>
          </div>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <AdvancedFilter
              onFilterChange={handleFilterChange}
              totalResults={filteredBlogs.length}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Trending Articles Section */}
      <TrendingArticles articles={trendingArticles} />

      {/* Author Spotlight Section */}
      <AuthorSpotlight authors={uniqueAuthors} />

      {/* Featured Blogs */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern bg-gradient-to-br from-muted/30 via-background to-muted/20"
      >
        <div className="container-modern">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-12">
            <div>
              <motion.h2
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="text-headline mb-3"
              >
                {currentFilters.searchQuery ? 'Search Results' : 'Featured Articles'}
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-muted-foreground"
              >
                Stay updated with our most recent insights and discoveries
              </motion.p>
            </div>
            {filteredBlogs.length > 6 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Button variant="outline" className="btn-modern group">
                  <span>Load More Articles</span>
                  <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </Button>
              </motion.div>
            )}
          </div>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <BlogsList blogs={filteredBlogs} />
          </motion.div>
        </div>
      </motion.section>

      {/* Newsletter Section */}
      <Newsletter />
    </>
  )
}

